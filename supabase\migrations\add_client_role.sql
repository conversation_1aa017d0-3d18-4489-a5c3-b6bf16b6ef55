/*
  # Add Client Role to User Role Enum
  
  1. Changes
    - Add 'client' to the user_role enum type
    - This allows users to have the client role in the database
  
  2. Security
    - Maintains existing RLS policies
    - Client role will have limited permissions (mainly for viewing their own projects)
*/

-- Add 'client' to the user_role enum
ALTER TYPE user_role ADD VALUE 'client';

-- Add comment to document the new role
COMMENT ON TYPE user_role IS 'User roles: employee (default), manager, admin, superadmin, client';

-- Log the update
DO $$
BEGIN
  RAISE NOTICE 'Added client role to user_role enum';
END $$;
