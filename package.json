{"name": "etams", "private": true, "author": "<PERSON>", "description": "EMS", "version": "0.0.0", "type": "module", "main": "electron/main.cjs", "scripts": {"dev": "vite", "build": "vite build", "server": "nodemon server.js", "lint": "eslint .", "preview": "vite preview", "electron:dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5173 && electron .\"", "electron:build": "vite build && electron-builder"}, "dependencies": {"@headlessui/react": "^2.2.1", "@hello-pangea/dnd": "^16.6.0", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@sendgrid/mail": "^8.1.4", "@supabase/supabase-js": "^2.39.7", "@tanstack/react-query": "^5.56.2", "@types/screenshot-desktop": "^1.12.3", "axios": "^1.8.0", "body-parser": "^1.20.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "concurrently": "^9.1.2", "cors": "^2.8.5", "cross-env": "^7.0.3", "date-fns": "^3.3.1", "date-fns-tz": "^3.2.0", "DatePicker": "^2.0.0", "dotenv": "^16.4.7", "electron": "^35.0.2", "electron-builder": "^25.1.8", "electron-is-dev": "^3.0.1", "electron-store": "^8.1.0", "embla-carousel-react": "^8.3.0", "express": "^4.21.2", "firebase": "^10.7.1", "firebase-admin": "^13.2.0", "formik": "^2.4.6", "framer-motion": "^12.19.1", "fs": "^0.0.1-security", "html-pdf": "^3.0.1", "input-otp": "^1.2.4", "lucide-react": "^0.344.0", "next-themes": "^0.3.0", "node-cron": "^3.0.3", "node-fetch": "^3.3.2", "nodemailer": "^6.10.0", "pdfkit": "^0.16.0", "pdfmake": "^0.2.18", "puppeteer": "^24.3.0", "react": "^18.3.1", "react-circular-progressbar": "^2.2.0", "react-datepicker": "^8.4.0", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-feather": "^2.0.10", "react-hook-form": "^7.53.0", "react-icons": "^5.5.0", "react-modal": "^3.16.3", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.22.2", "react-select": "^5.10.1", "react-time-picker": "^7.0.0", "react-tooltip": "^5.28.0", "recharts": "^2.12.2", "screenshot-desktop": "^1.15.1", "sonner": "^1.5.0", "supabase": "^2.15.8", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^0.9.3", "wait-on": "^8.0.3", "web-push": "^3.6.7", "ws": "^8.18.1", "yup": "^1.6.1", "zod": "^3.23.8", "zustand": "^4.5.2"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/node": "^22.12.0", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "7zip-bin": "^5.2.0", "autoprefixer": "^10.4.18", "electron": "^35.0.2", "electron-builder": "^25.1.8", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "rollup": "^4.37.0", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.14"}, "engines": {"node": "v22.14.0", "npm": "v10.9.2"}, "build": {"appId": "electron-react-vite", "win": {"target": ["nsis"]}, "files": ["dist/**/**", "electron/electron.cjs", "node_modules/**/**", "package.json"], "nsis": {"uninstallDisplayName": "Uninstall this app", "license": "license.txt", "oneClick": "false", "allowToChangeInstallationDirectory": "true"}, "directories": {"output": "releases", "buildResources": "dist"}}}