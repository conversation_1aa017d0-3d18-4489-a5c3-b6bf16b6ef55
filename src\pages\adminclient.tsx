import React, { useState } from 'react';
import { FiPlus, FiTrash2 } from 'react-icons/fi';
import AddClientModal from '../components/addclientModal';

// Dummy client data
const clients = [
    {
        id: 1,
        name: '<PERSON>',
        email: '<PERSON><PERSON><PERSON>@techcreator.co',
        projects: ['PatronWorks - Angular', 'Learn new Technologies'],
        workload: 45,
    },
    {
        id: 2,
        name: 'ihtizaz',
        email: '<EMAIL>',
        projects: ['PatronWorks - Angular'],
        workload: 2,
    },
];

const AdminClient: React.FC = () => {
    const [modalOpen, setModalOpen] = useState(false);
    return (
        <div className="p-6 bg-white max-w-7xl mx-auto">
            <div className="flex justify-between items-center">
                <div>
                    <h1 className="text-2xl font-bold text-gray-900">Client Management</h1>
                    <p className="mt-1 text-sm text-gray-500">View and manage your Client here</p>
                </div>
                <div className="flex items-center space-x-3">
                    <input
                        type="text"
                        placeholder="Search Client"
                        className="px-4 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    />
                    <button
                        className="px-4 py-2 text-sm font-medium text-white bg-purple-600 rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
                        onClick={() => setModalOpen(true)}
                    >
                        + Add Client
                    </button>
                    <AddClientModal isOpen={modalOpen} onClose={() => setModalOpen(false)} />
                </div>
            </div>

            {/* Table */}
            <div className="mt-8">
                <div className="overflow-x-auto rounded-lg border border-gray-200">
                    <table className="min-w-full bg-white">
                        <thead>
                            <tr className="bg-gray-50">
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Projects</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Workload</th>
                                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {clients.map((client) => (
                                <tr key={client.id} className="border-b">
                                    {/* Employee */}
                                    <td className="px-6 py-4 whitespace-nowrap flex items-center">
                                        <div className="flex-shrink-0 h-10 w-10 rounded-full bg-purple-600 flex items-center justify-center text-white font-bold uppercase mr-4">
                                            {client.name.charAt(0)}
                                        </div>
                                        <div>
                                            <div className="text-sm font-medium text-gray-900">{client.name}</div>
                                            <div className="text-sm text-gray-500">{client.email}</div>
                                        </div>
                                    </td>
                                    {/* Projects */}
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="flex flex-col gap-1">
                                            {client.projects.map((project, idx) => (
                                                <span
                                                    key={idx}
                                                    className="inline-block bg-purple-50 text-purple-700 text-xs px-3 py-1 rounded-full"
                                                >
                                                    {project}
                                                </span>
                                            ))}
                                        </div>
                                    </td>
                                    {/* Workload */}
                                    <td className="px-6 py-4 whitespace-nowrap text-gray-900 font-semibold">
                                        {client.workload}
                                    </td>
                                    {/* Actions */}
                                    <td className="px-6 py-4 whitespace-nowrap text-center">

                                        <button className="inline-flex items-center justify-center w-8 h-8 rounded-md bg-red-100 text-red-600 hover:bg-red-200">
                                            <FiTrash2 size={18} />
                                        </button>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    );
};

export default AdminClient;